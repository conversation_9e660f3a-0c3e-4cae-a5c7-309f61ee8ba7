/* Reset and Base Styles */
:root {
    --rid-cod-primary-bg-color: #ffffff; /* Default Primary Background */
    --rid-cod-button-bg-color: #6a3de8; /* Default Button Background */
    --rid-cod-button-text-color: #ffffff; /* Default Button Text */
    --rid-cod-accent-color: #6a3de8; /* Default Accent Color */
    --rid-cod-border-color: #e0e0e0; /* Default Border Color */
    --rid-cod-text-color: #333333; /* Default Text Color */
    --rid-cod-label-color: #555555; /* Default Label Color */
    --rid-cod-error-color: #dc3232; /* Default Error Color */
    --rid-cod-success-color: #46b450; /* Default Success Color */

    /* Sticky Button Colors */
    --rid-cod-sticky-button-bg-color: #6a3de8; /* Default Sticky Button Background */
    --rid-cod-sticky-button-text-color: #ffffff; /* Default Sticky Button Text */
    --rid-cod-sticky-button-border-color: #6a3de8; /* Default Sticky Button Border */
}

#rid-cod-checkout *,
#rid-cod-checkout *:before,
#rid-cod-checkout *:after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Main container styles */
#rid-cod-checkout {
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    padding: 25px;
    border-radius: 8px; /* Slightly less rounded */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07); /* Softer shadow */
    margin-bottom: 30px;
    max-width: 100%;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif; /* Prioritize Cairo if available */
    direction: rtl;
    border-top: 4px solid var(--rid-cod-accent-color); /* Use CSS Variable */
}

/* Title Styles */
.rid-cod-title h3 {
    font-size: 18px; /* Smaller title */
    margin-bottom: 25px;
    text-align: center;
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    font-weight: 700; /* Bolder */
    position: relative;
    padding-bottom: 0; /* Remove padding */
}

.rid-cod-title h3:after {
    display: none; /* Remove underline */
}

.form-title-icon {
    display: inline-block;
    margin-right: 5px; /* Space before icon */
}

/* Form layout */
#rid-cod-form {
    display: flex;
    flex-direction: column;
    grid-gap: 15px; /* Reduced gap */
}

.rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px; /* Reduced gap */
    margin-bottom: 0; /* Remove bottom margin */
}

/* Field group and input styles */
.rid-cod-field-group {
    margin-bottom: 0; /* Remove bottom margin */
    position: relative; /* Needed for icon positioning */
}

.rid-cod-field-group label {
    display: none; /* Hide labels as per design */
}

#rid-cod-form input[type="text"],
#rid-cod-form input[type="tel"],
#rid-cod-form input[type="email"],
#rid-cod-form select,
#rid-cod-form textarea {
    width: 100%;
    padding: 12px 40px 12px 15px; /* Adjust padding for icon */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-radius: 6px; /* Less rounded */
    font-size: 14px; /* Slightly smaller font */
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-shadow: none; /* Remove inner shadow */
    height: 48px; /* Fixed height */
}

#rid-cod-form select {
    padding-right: 45px !important; /* Ensure space for icon and arrow */
    padding-left: 15px !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center; /* Arrow on the left for RTL */
    background-size: 16px 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

#rid-cod-form input:focus,
#rid-cod-form select:focus,
#rid-cod-form textarea:focus {
    border-color: #80bdff; /* Standard focus color */
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Standard focus shadow */
}

/* Input Icons */
.rid-cod-field-with-icon .rid-input-icon {
    position: absolute;
    top: 50%;
    right: 15px; /* Position icon on the right */
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.6;
}

.rid-icon-user { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10s-3.516.68-4.168 1.332c-.678.678-.83 1.418-.832 1.664z"/></svg>'); }
.rid-icon-phone { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/></svg>'); }
.rid-icon-state { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path fill-rule="evenodd" d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/></svg>'); }
.rid-icon-city { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path d="M8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4.5a.5.5 0 0 0 .5-.5v-4h2v4a.5.5 0 0 0 .5.5H14a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354zM2.5 14V7.707l5.5-5.5 5.5 5.5V14H10v-4a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5v4z"/></svg>'); }
.rid-icon-cart { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-cart-fill" viewBox="0 0 16 16"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5M5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4m7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2m7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/></svg>'); }
.rid-icon-arrow-down { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-chevron-down" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/></svg>'); }
.rid-icon-arrow-up { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-chevron-up" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/></svg>'); }
.rid-icon-whatsapp { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16"><path d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.1-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"/></svg>'); }

/* Variations (Original - Kept for reference, but new styles below take precedence) */
/*
.rid-cod-variations {
    margin-bottom: 15px;
}
.rid-cod-variations select {
    margin-top: 5px;
}
*/

/* WooCommerce Compatible Variations Styles - Enhanced Design */
.rid-cod-variations {
    margin-bottom: 20px;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.rid-cod-variations:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #dee2e6;
}

/* Ensure WooCommerce variation form is visible */
.rid-cod-variations .variations_form {
    display: block !important;
}

/* Style the variations table with modern design */
.rid-cod-variations .variations {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.rid-cod-variations .variations td {
    padding: 12px 0;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.rid-cod-variations .variations tr:last-child td {
    border-bottom: none;
}

.rid-cod-variations .variations td.label {
    width: 35%;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    padding-right: 15px;
}

.rid-cod-variations .variations td.value {
    width: 65%;
}

/* Box-style variations display */
.rid-cod-variations .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.rid-cod-variations .variation-option {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 85px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    color: #495057;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.rid-cod-variations .variation-option:hover {
    border-color: #c1c9d0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

.rid-cod-variations .variation-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
    color: var(--rid-cod-accent-color);
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(106, 61, 232, 0.15);
}

/* For color variations */
.rid-cod-variations .variation-option.color-option {
    min-width: 50px;
    min-height: 50px;
    padding: 5px;
    border-radius: 50%;
}

/* Error highlight for variation boxes */
.rid-cod-variations .variation-boxes.error-highlight {
    animation: shake 0.5s ease-in-out;
    border: 2px dashed var(--rid-cod-error-color);
    border-radius: 8px;
    padding: 8px;
    background-color: rgba(220, 50, 50, 0.05);
}

.rid-cod-variations .variation-boxes.error-highlight .variation-option {
    border-color: rgba(220, 50, 50, 0.3);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Disabled variation options */
.rid-cod-variations .variation-option.disabled,
.rid-cod-variations .variation-option.unavailable {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

/* Animation for becoming disabled/enabled */
.rid-cod-variations .variation-option.becoming-disabled {
    animation: fadeToDisabled 0.3s ease-out forwards;
}

.rid-cod-variations .variation-option.becoming-enabled {
    animation: fadeToEnabled 0.3s ease-out forwards;
}

@keyframes fadeToDisabled {
    from { opacity: 1; }
    to { opacity: 0.4; }
}

@keyframes fadeToEnabled {
    from { opacity: 0.4; }
    to { opacity: 1; }
}

/* For color variations - Original Design (Circles Only) */
.rid-cod-variations .variation-option.color-option {
    position: relative;
    width: 55px;
    height: 55px;
    min-width: 55px;
    padding: 0;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rid-cod-variations .variation-option.color-option .color-swatch {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Hide color names in color options */
.rid-cod-variations .variation-option.color-option .color-name {
    display: none;
}

.rid-cod-variations .variation-option.color-option.selected {
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 2px var(--rid-cod-accent-color);
}

.rid-cod-variations .variation-option.color-option.selected:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.5));
}

/* Hide original select elements but keep them in DOM for form submission */
.rid-cod-variations .variations select {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
    pointer-events: none;
}

/* Attribute label styling */
.rid-cod-variations .attribute-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    margin-top: 15px;
}

/* Modern dropdown styling matching the plugin design */
.rid-cod-variations .variations select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    font-size: 14px;
    color: #495057;
    font-family: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="%23495057" viewBox="0 0 16 16"><path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    padding-left: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* RTL support for dropdown arrow */
.rtl .rid-cod-variations .variations select {
    background-position: right 12px center;
    padding-right: 40px;
    padding-left: 16px;
}

/* RTL support for labels */
.rtl .rid-cod-variations .variations td.label {
    padding-left: 15px;
    padding-right: 0;
    text-align: right;
}

/* Order Summary Table Styles */
.rid-cod-order-summary table,
.rid-cod-order-summary tr,
.rid-cod-order-summary td {
    border: none !important;
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    box-shadow: none !important;
    background: transparent !important;
}

.rid-cod-order-summary table {
    margin: 0 !important;
    width: 100% !important;
}

.rid-cod-order-summary td {
    padding: 10px !important;
    vertical-align: middle !important;
}

.rid-cod-order-summary tr:last-child {
    font-weight: bold;
}

.rid-cod-order-summary tr:last-child td {
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding-top: 15px !important;
}

/* Accessibility improvements */
.rid-cod-variations .variations select:focus-visible {
    outline: 2px solid var(--rid-cod-accent-color);
    outline-offset: 2px;
}

.rid-cod-variations .variations select[aria-invalid="true"] {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15);
}

/* Loading state for variations */
.rid-cod-variations.loading {
    opacity: 0.7;
    pointer-events: none;
}

.rid-cod-variations.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--rid-cod-accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.rid-cod-variations .variations select:focus {
    border-color: var(--rid-cod-accent-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
    transform: translateY(-1px);
}

.rid-cod-variations .variations select:hover {
    border-color: #ced4da;
    background-color: #f8f9fa;
}

/* Style the single variation display */
.rid-cod-variations .single_variation_wrap {
    margin-top: 15px;
    padding: 0;
    background: transparent;
    border: none;
}

.rid-cod-variations .single_variation_wrap .woocommerce-variation {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 13px;
    color: #6c757d;
}

/* Reset variations link */
.rid-cod-variations .reset_variations {
    font-size: 12px;
    color: #6c757d;
    text-decoration: none;
    margin-top: 8px;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.rid-cod-variations .reset_variations:hover {
    color: var(--rid-cod-accent-color);
    background-color: rgba(106, 61, 232, 0.1);
    text-decoration: none;
}

/* Responsive Design for Variations */
@media (max-width: 768px) {
    .rid-cod-variations {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 10px;
    }

    .rid-cod-variations .variations td.label {
        width: 100%;
        padding-right: 0;
        padding-bottom: 6px;
        font-size: 13px;
    }

    .rid-cod-variations .variations td.value {
        width: 100%;
        padding-top: 0;
    }

    .rid-cod-variations .variations td {
        display: block;
        padding: 6px 0;
    }

    .rid-cod-variations .variations select {
        padding: 10px 14px;
        padding-left: 36px;
        font-size: 13px;
    }

    .rtl .rid-cod-variations .variations select {
        padding-right: 36px;
        padding-left: 14px;
    }
}

/* Enhanced variation section title */
.rid-cod-variations::before {
    content: "🎨 " attr(data-title);
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f1f3f4;
}

/* Add subtle animation on variation change */
.rid-cod-variations .variations select.changing {
    transform: scale(1.02);
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
}

/* Animation when variation is updated */
.rid-cod-variations.variation-updated {
    transform: scale(1.01);
    box-shadow: 0 6px 20px rgba(106, 61, 232, 0.15);
    border-color: var(--rid-cod-accent-color);
}

/* Smooth transitions for all variation elements */
.rid-cod-variations,
.rid-cod-variations .variations select,
.rid-cod-variations .single_variation_wrap {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}


.rid-cod-delivery-type label {
    display: block; /* Make the main label block */
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--rid-cod-label-color); /* Use CSS Variable */
    font-size: 14px;
}
.rid-delivery-options {
    display: flex;
    gap: 15px; /* Space between radio buttons */
}
.rid-delivery-options label {
    display: flex; /* Align radio button and text */
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    cursor: pointer;
    font-size: 14px;
}
.rid-delivery-options input[type="radio"] {
    margin-left: 5px; /* Space after radio button (RTL) */
    cursor: pointer;
}


/* Actions Row (Submit Button and Quantity) */
.rid-cod-actions-row {
    display: grid;
    grid-template-columns: 1fr auto; /* Submit button takes remaining space */
    grid-gap: 15px;
    align-items: center;
    margin-top: 10px; /* Space above actions */
}

/* Quantity selector styles */
.rid-cod-quantity {
    display: flex; /* Use flex for internal alignment */
    align-items: center;
    justify-content: center; /* Center items horizontally */
}

.rid-cod-quantity-selector {
    display: flex; /* Use flex for button/input alignment */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-radius: 6px;
    overflow: hidden;
    height: 48px; /* Match input height */
}

#rid-cod-decrease,
#rid-cod-increase {
    background: #f8f9fa; /* Lighter background */
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--rid-cod-accent-color); /* Use CSS Variable */
    width: 40px; /* Fixed width */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

#rid-cod-decrease:hover,
#rid-cod-increase:hover {
    background: #e9ecef; /* Slightly darker on hover */
}

#rid-cod-quantity-input {
    text-align: center;
    border: none;
    border-left: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-right: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    padding: 5px;
    font-size: 16px; /* Slightly smaller */
    font-weight: 600;
    width: 50px; /* Fixed width */
    height: 100%; /* Fill height */
    -moz-appearance: textfield; /* Hide arrows for number input */
    appearance: textfield; /* Standard property for compatibility */
}
#rid-cod-quantity-input::-webkit-outer-spin-button,
#rid-cod-quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Submit button styles */
.rid-cod-submit {
    display: flex; /* Use flex to position the button */
    justify-content: stretch; /* Stretch button to fill space */
    align-items: center; /* Center button vertically */
}

#rid-cod-submit-btn {
    width: 100%;
    padding: 0 15px; /* Adjust padding */
    height: 48px; /* Match input height */
    background-color: var(--rid-cod-button-bg-color); /* Use CSS Variable */
    color: var(--rid-cod-button-text-color); /* Use CSS Variable */
    border: none;
    border-radius: 6px;
    font-size: 16px; /* Slightly smaller */
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.1s ease;
    box-shadow: 0 4px 10px rgba(106, 61, 232, 0.3);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase; /* Uppercase text like image */
}

#rid-cod-submit-btn:hover {
    /* Consider adding a slightly darker version of the button bg color variable */
    background-color: #5a30d8; /* Darker purple on hover - TODO: Make dynamic? */
    box-shadow: 0 6px 15px rgba(106, 61, 232, 0.4);
    transform: translateY(-2px);
}

#rid-cod-submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(106, 61, 232, 0.3);
}

/* WhatsApp Button Styles */
.rid-cod-whatsapp-button {
    margin-top: 15px; /* Space above WhatsApp button */
}

#rid-cod-whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 15px;
    height: 48px;
    background-color: #25d366; /* WhatsApp green */
    color: #fff;
    border: none;
    border-radius: 24px; /* Pill shape */
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 10px rgba(37, 211, 102, 0.3);
    text-decoration: none;
}

#rid-cod-whatsapp-btn .rid-icon-whatsapp {
    width: 20px; /* Larger icon */
    height: 20px;
    margin-left: 8px; /* Space between icon and text */
    display: inline-block;
    vertical-align: middle;
    filter: brightness(0) invert(1); /* Make SVG white */
}

#rid-cod-whatsapp-btn:hover {
    background-color: #1ebe5b; /* Darker green on hover */
    box-shadow: 0 6px 15px rgba(37, 211, 102, 0.4);
}

/* Order summary styles */
#rid-cod-summary-wrapper {
    margin-top: 25px;
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    border-radius: 8px;
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

#rid-cod-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px; /* Slightly less padding */
    cursor: pointer;
    border-bottom: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    background-color: #f8f9fa; /* Light grey header background */
    transition: background-color 0.2s ease;
}
#rid-cod-summary-header:hover {
    background-color: #e9ecef; /* Darken header on hover */
}
/* Keep border when open/closed, managed by header style */
/* #rid-cod-summary-header.open { } */
/* #rid-cod-summary-header:not(.open) { border-bottom: none; } */


#rid-cod-summary-header h4 {
    font-size: 15px; /* Slightly smaller */
    margin-bottom: 0;
    color: var(--rid-cod-label-color); /* Use CSS Variable */
    font-weight: 700;
    display: flex;
    align-items: center;
}

#rid-cod-summary-header h4 .rid-icon-cart {
    width: 18px;
    height: 18px;
    margin-left: 8px; /* Space after icon */
}

#rid-cod-summary-toggle {
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transition: transform 0.3s ease;
}
#rid-cod-summary-toggle.open {
    transform: rotate(180deg);
}


#rid-cod-summary-content {
    padding: 15px;
    /* display: none; */ /* Controlled by JS */
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
}

#rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

#rid-cod-summary-content td {
    padding: 10px 0; /* Increased vertical padding */
    border-bottom: 1px solid #e9ecef; /* Lighter solid separator */
    font-size: 14px;
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    vertical-align: top;
    word-wrap: break-word;
}

#rid-cod-summary-content td:first-child {
    max-width: 65%;
    overflow-wrap: break-word;
    word-break: break-word;
}

#rid-cod-summary-content td:last-child {
    text-align: left;
    min-width: 35%;
}

#rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    padding-top: 15px; /* More space before total */
}

#rid-cod-summary-content tr.rid-cod-total td {
    font-weight: 700; /* Bolder total */
    color: var(--rid-cod-accent-color); /* Use CSS Variable */
    font-size: 17px; /* Larger total */
}

#rid-cod-summary-content tr td:first-child {
    text-align: right;
}

#rid-cod-summary-content tr td:last-child {
    text-align: left;
    font-weight: 500; /* Normal weight for prices */
}

/* Product name styling in summary */
.product-name {
    display: block;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 3px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Variation details in summary */
.rid-summary-variation-details {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.rid-summary-variation-details .variation-detail,
.rid-summary-variation-details .rid-variation-meta {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 3px;
    padding: 2px 6px;
    background-color: #f8f9fa;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive handling for long product names and variation details */
@media (max-width: 768px) {
    #rid-cod-summary-content {
        padding: 12px;
    }
    
    #rid-cod-summary-content td {
        font-size: 13px;
        padding: 8px 0;
    }
    
    #rid-cod-summary-content td:first-child {
        max-width: 60%;
        word-wrap: break-word;
        white-space: normal;
        line-height: 1.3;
    }
    
    #rid-cod-summary-content td:last-child {
        min-width: 40%;
        text-align: left;
    }
    
    .product-name {
        font-size: 13px;
        line-height: 1.2;
    }
    
    .rid-summary-variation-details .variation-detail,
    .rid-summary-variation-details .rid-variation-meta {
        display: block;
        white-space: normal;
        max-width: 100%;
        margin-bottom: 2px;
        margin-right: 0;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    #rid-cod-summary-content {
        padding: 10px;
    }
    
    #rid-cod-summary-content td {
        font-size: 12px;
        padding: 6px 0;
    }
    
    #rid-cod-summary-content td:first-child {
        max-width: 55%;
    }
    
    #rid-cod-summary-content td:last-child {
        min-width: 45%;
        font-size: 11px;
    }
    
    .product-name {
        font-size: 12px;
        line-height: 1.1;
        margin-bottom: 4px;
    }
    
    .rid-summary-variation-details {
        font-size: 10px;
        margin-top: 3px;
    }
    
    .rid-summary-variation-details .variation-detail,
    .rid-summary-variation-details .rid-variation-meta {
        font-size: 9px;
        padding: 1px 4px;
        margin-bottom: 1px;
    }
}

.rid-summary-quantity {
    display: inline-block;
    background-color: #e9ecef; /* Light grey background */
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    font-size: 12px; /* Slightly larger */
    padding: 3px 8px; /* More padding */
    border-radius: 4px;
    margin-left: 8px; /* More space */
    font-weight: normal;
    vertical-align: middle;
}

/* Message styles */
#rid-cod-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 6px;
    display: none;
    font-size: 14px;
}

#rid-cod-message.success {
    background-color: #d4edda;
    color: var(--rid-cod-success-color); /* Use CSS Variable */
    border: 1px solid #c3e6cb;
    display: block;
}

#rid-cod-message.error {
    background-color: #f8d7da;
    color: var(--rid-cod-error-color); /* Use CSS Variable */
    border: 1px solid #f5c6cb;
    display: block;
}

/* Global message styling */
.rid-global-message {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    z-index: 10000 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    font-weight: 500 !important;
    max-width: 350px !important;
    word-wrap: break-word !important;
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif !important;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .rid-global-message {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        font-size: 13px !important;
        padding: 10px 15px !important;
    }
}

/* Success Popup Styles */
.rid-cod-popup {
    position: fixed; /* Position relative to the viewport */
    top: 20px; /* Position from the top */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Adjust horizontal centering */
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 99999; /* Ensure it's on top of other elements */
    display: none; /* Hidden by default, shown via JS */
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    max-width: 90%; /* Prevent it from being too wide on small screens */
    box-sizing: border-box;
}

.rid-cod-popup.success {
    background-color: #d4edda; /* Light green background */
    color: var(--rid-cod-success-color); /* Use CSS Variable */
    border: 1px solid #c3e6cb;
}

/* Optional: Add a subtle transition for fade-in/out if not handled solely by JS */
.rid-cod-popup {
    transition: opacity 0.5s ease-in-out;
}

/* Responsive styles */
@media (max-width: 768px) {
    #rid-cod-checkout {
        padding: 20px 15px;
    }
    .rid-cod-customer-info {
        grid-template-columns: 1fr; /* Stack fields on mobile */
    }
    .rid-cod-actions-row {
        grid-template-columns: 1fr; /* Stack actions on mobile */
        grid-gap: 10px;
    }
    .rid-cod-submit {
        order: 2; /* Place submit button below quantity */
    }
    .rid-cod-quantity {
        order: 1;
        justify-content: center; /* Center quantity selector */
    }
    #rid-cod-submit-btn,
    #rid-cod-whatsapp-btn {
        font-size: 15px;
        height: 44px;
    }
    #rid-cod-summary-header h4 {
        font-size: 15px;
    }
    #rid-cod-summary-content td {
        font-size: 13px;
    }
}

/* --- Sticky Buy Button --- */
.rid-cod-sticky-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--rid-cod-primary-bg-color, #fff);
    padding: 20px 25px;
    box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(100%); /* Initially hidden */
    transition: transform 0.3s ease-in-out;
    border-top: 1px solid var(--rid-cod-border-color, #e0e0e0);
    direction: rtl;
    /* Centering properties */
    display: flex;
    justify-content: center;
    align-items: center;
}

.rid-cod-sticky-button-container.visible {
    transform: translateY(0);
    display: flex; /* Keep flex properties when visible */
}

#rid-cod-sticky-button-container #rid-cod-sticky-submit-btn {
    background-color: var(--rid-cod-sticky-button-bg-color);
    color: var(--rid-cod-sticky-button-text-color);
    border: none;
    border-radius: 8px;
    padding: 16px 40px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 61, 232, 0.3);
    text-transform: uppercase;
    min-width: 280px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

/* Override hover effect if needed, or let it inherit */
/* .rid-cod-sticky-button-container #rid-cod-submit-btn:hover {
    opacity: 0.9;
} */

/* Responsive adjustments for sticky button */
@media (max-width: 768px) {
    .rid-cod-sticky-button-container {
        padding: 10px 15px;
    }
    /* Button inherits responsive styles from base rule */
}

/* RTL fixes if needed */
.rtl #rid-cod-form select {
    background-position: left 0.75rem center;
    padding-right: 45px !important;
    padding-left: 15px !important;
}
.rtl .rid-cod-field-with-icon .rid-input-icon {
    right: 15px;
    left: auto;
}
.rtl #rid-cod-form input[type="text"],
.rtl #rid-cod-form input[type="tel"],
.rtl #rid-cod-form input[type="email"],
.rtl #rid-cod-form select,
.rtl #rid-cod-form textarea {
    padding: 12px 40px 12px 15px; /* R L T B */
}
.rtl #rid-cod-whatsapp-btn .rid-icon-whatsapp {
    margin-left: 8px;
    margin-right: 0;
}
.rtl #rid-cod-summary-header h4 .rid-icon-cart {
     margin-left: 8px;
     margin-right: 0;
}
.rtl #rid-cod-summary-content tr td:first-child {
    text-align: right;
}
.rtl #rid-cod-summary-content tr td:last-child {
    text-align: left;
}

/* Variation Swatch Styles */
.rid-cod-variation-row {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;
}
.rid-cod-variation-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rid-cod-variation-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.rid-cod-swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.rid-cod-swatch {
    display: inline-flex; /* Use inline-flex for alignment */
    align-items: center;
    justify-content: center;
    min-width: 40px; /* Minimum width */
    height: 40px; /* Fixed height */
    padding: 5px 15px; /* Padding for text */
    border: 2px solid #ced4da; /* Default border */
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    background-color: #fff;
    text-align: center;
    line-height: 1.5; /* Adjust line height */
}

.rid-cod-swatch:hover {
    border-color: #adb5bd; /* Darker border on hover */
}

.rid-cod-swatch.selected {
    border-color: #6a3de8; /* Purple border when selected */
    background-color: #f3efff; /* Light purple background */
    color: #6a3de8; /* Purple text */
    font-weight: 700;
    box-shadow: 0 0 0 2px rgba(106, 61, 232, 0.2); /* Subtle glow */
}

/* Color Swatches */
.rid-cod-swatch.rid-color-swatch {
    width: 40px; /* Fixed width for color swatches */
    min-width: 40px;
    padding: 0; /* Remove padding */
    overflow: hidden; /* Hide text if any */
    text-indent: -9999px; /* Hide text */
    position: relative; /* For pseudo-elements */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Subtle shadow for all color swatches */
}

/* Special handling for white/light colors */
.rid-cod-swatch.rid-color-swatch.rid-swatch-white,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fff"],
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffffff"],
.rid-cod-swatch.rid-color-swatch[style*="background-color: rgb(255, 255, 255)"] {
    box-shadow: inset 0 0 0 1px #ddd; /* Inner border for better visibility */
}

/* Enhance visibility of selected light colors */
.rid-cod-swatch.rid-color-swatch.selected {
    box-shadow: 0 0 0 2px rgba(106, 61, 232, 0.7), inset 0 0 0 1px rgba(0,0,0,0.1); /* Stronger accent color glow */
}

/* Checkmark for selected colors */
.rid-cod-swatch.rid-color-swatch.selected:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.9;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5)); /* Make checkmark visible on any background */
}

/* Invert checkmark for light colors */
.rid-cod-swatch.rid-color-swatch.rid-swatch-white.selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffffff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: rgb(255, 255, 255)"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffd"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffc"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffa"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ff9"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ff8"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #eee"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f5f5"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f0e6"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fffffe0"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fffff0"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f5f5dc"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffdab9"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffd580"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #d3d3d3"].selected:after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    opacity: 0.7;
}

/* Add hover effect for color swatches */
.rid-cod-swatch.rid-color-swatch:hover {
    transform: scale(1.08);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

/* Improved size swatches (text-based) */
.rid-cod-swatch.rid-size-swatch {
    /* Uses default swatch styles */
    min-width: 30px; /* Slightly smaller minimum width */
    padding: 5px 10px; /* Adjusted padding */
}

/* Disabled Swatches (Enhanced for linked variations) */
.rid-cod-swatch.disabled,
.rid-cod-variations .variation-option.disabled {
    opacity: 0.4;
    cursor: not-allowed !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    pointer-events: none;
    position: relative;
}

.rid-cod-swatch.disabled:hover,
.rid-cod-variations .variation-option.disabled:hover {
    border-color: #dee2e6 !important; /* Prevent hover effect */
    transform: none !important;
    box-shadow: none !important;
}

/* Enhanced message styling for cleared selections */
.rid-selection-cleared-message {
    position: absolute !important;
    top: -35px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(220, 53, 69, 0.95) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

/* Enhanced disabled styling for better visual feedback */
.rid-cod-variations .variation-option.disabled,
.rid-cod-swatch.disabled {
    opacity: 0.3 !important;
    cursor: not-allowed !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    pointer-events: none !important;
    position: relative;
    filter: grayscale(70%);
}

/* Improved strikethrough effect for unavailable options */
.rid-cod-variations .variation-option.unavailable::after,
.rid-cod-swatch.unavailable::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 8%;
    right: 8%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #dc3545 20%, #dc3545 80%, transparent);
    transform: translateY(-50%) rotate(-15deg);
    z-index: 2;
    box-shadow: 0 0 3px rgba(220, 53, 69, 0.5);
}

/* Enhanced disabled color swatch styling */
.rid-cod-swatch.rid-color-swatch.disabled {
    opacity: 0.25 !important;
    filter: grayscale(100%) brightness(1.2);
}

.rid-cod-swatch.rid-color-swatch.disabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.9) 2px,
        rgba(255,255,255,0.9) 4px,
        transparent 4px,
        transparent 6px,
        rgba(220, 53, 69, 0.7) 6px,
        rgba(220, 53, 69, 0.7) 8px
    );
    z-index: 2;
    border-radius: inherit;
}

/* Removed first attribute highlighting styles */

/* Selection cleared message */
.rid-selection-cleared-message {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(220, 53, 69, 0.95);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.2);
    pointer-events: none;
}

/* Global selection message */
.rid-global-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(23, 162, 184, 0.95);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 300px;
    word-wrap: break-word;
}

/* Additional Custom Styles */
/* Add any further custom styles below */
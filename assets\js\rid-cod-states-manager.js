jQuery(document).ready(function($) {
    'use strict';

    var currentCountry = rid_cod_states_params.current_country;
    var statesTable = $('.form-table tbody');

    // Get default shipping costs for placeholders
    var defaultDeskCost = $('#rid_cod_default_desk_cost').val() || '0.00';
    var defaultHomeCost = $('#rid_cod_default_home_cost').val() || '0.00';

    // Get currency symbol from the page
    var currencySymbol = $('.form-table input[name*="desk"]').siblings().filter(function() {
        return $(this).text().trim().length <= 3; // Currency symbols are usually short
    }).first().text().trim() || '';

    // Initialize states manager
    function initStatesManager() {
        // Make states table sortable
        if (statesTable.length > 0) {
            statesTable.sortable({
                handle: '.drag-handle',
                placeholder: 'ui-sortable-placeholder',
                helper: function(e, ui) {
                    ui.children().each(function() {
                        $(this).width($(this).width());
                    });
                    return ui;
                },
                update: function(event, ui) {
                    $('#save-states-order').show();
                }
            });
        }

        // Watch for changes in default costs to update placeholders
        $('#rid_cod_default_desk_cost, #rid_cod_default_home_cost').on('input', function() {
            updateDefaultCosts();
        });
    }

    // Update default costs in variables and placeholders
    function updateDefaultCosts() {
        defaultDeskCost = $('#rid_cod_default_desk_cost').val() || '0.00';
        defaultHomeCost = $('#rid_cod_default_home_cost').val() || '0.00';

        // Update placeholders in existing inputs
        $('.rid-cod-state-desk-cost').attr('placeholder', defaultDeskCost);
        $('.rid-cod-state-home-cost').attr('placeholder', defaultHomeCost);

        // Update description texts
        $('.rid-cod-state-desk-cost').siblings('.description').text(`(افتراضي: ${parseFloat(defaultDeskCost).toFixed(2)})`);
        $('.rid-cod-state-home-cost').siblings('.description').text(`(افتراضي: ${parseFloat(defaultHomeCost).toFixed(2)})`);
    }

    // Add state to table dynamically
    function addStateToTable(stateCode, stateName) {
        var newRow = `
            <tr valign="top" class="state-row" data-state-code="${stateCode}" data-state-name="${stateName}">
                <td class="drag-handle" style="cursor: move; text-align: center; color: #666;">⋮⋮</td>
                <th scope="row">${stateName} (${stateCode})</th>
                <td>
                    <input type="number" step="any" min="0"
                           name="rid_cod_shipping_costs_${currentCountry}[${stateCode}][desk]"
                           value=""
                           class="small-text rid-cod-state-desk-cost"
                           data-state="${stateCode}"
                           placeholder="${defaultDeskCost}" />
                    ${currencySymbol}
                    <span class="description">(افتراضي: ${parseFloat(defaultDeskCost).toFixed(2)})</span>
                </td>
                <td>
                    <input type="number" step="any" min="0"
                           name="rid_cod_shipping_costs_${currentCountry}[${stateCode}][home]"
                           value=""
                           class="small-text rid-cod-state-home-cost"
                           data-state="${stateCode}"
                           placeholder="${defaultHomeCost}" />
                    ${currencySymbol}
                    <span class="description">(افتراضي: ${parseFloat(defaultHomeCost).toFixed(2)})</span>
                </td>
                <td class="state-actions">
                    <button type="button" class="button button-small edit-state"
                            data-state-code="${stateCode}"
                            data-state-name="${stateName}"
                            title="تعديل الولاية">
                        <span class="dashicons dashicons-edit"></span>
                    </button>
                    <button type="button" class="button button-small delete-state"
                            data-state-code="${stateCode}"
                            data-state-name="${stateName}"
                            title="حذف الولاية">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </td>
            </tr>
        `;

        // Add to the table
        statesTable.append(newRow);

        // Re-initialize sortable if needed
        if (statesTable.hasClass('ui-sortable')) {
            statesTable.sortable('refresh');
        }

        // Show save button
        $('#save-states-order').show();
    }

    // Update state in table dynamically
    function updateStateInTable(oldCode, newCode, newName) {
        var row = statesTable.find(`tr[data-state-code="${oldCode}"]`);
        
        if (row.length) {
            // Update data attributes
            row.attr('data-state-code', newCode);
            row.attr('data-state-name', newName);
            
            // Update row header text
            row.find('th').text(`${newName} (${newCode})`);
            
            // Update input names and data attributes
            row.find('input[name*="desk"]').attr('name', `rid_cod_shipping_costs_${currentCountry}[${newCode}][desk]`).attr('data-state', newCode);
            row.find('input[name*="home"]').attr('name', `rid_cod_shipping_costs_${currentCountry}[${newCode}][home]`).attr('data-state', newCode);
            
            // Update button data attributes
            row.find('.edit-state, .delete-state').attr('data-state-code', newCode).attr('data-state-name', newName);
            
            // Show the save button
            $('#save-states-order').show();
        } else {
            // If row not found, refresh the table
            refreshStatesTable();
        }
    }

    // Refresh page to reload states table (fallback)
    function refreshStatesTable() {
        // Simple page reload to refresh the table
        setTimeout(function() {
            window.location.reload();
        }, 500);
    }

    // Show error message
    function showError(message) {
        alert('خطأ: ' + message);
    }

    // Show success message
    function showSuccess(message) {
        // You can replace this with a better notification system
        alert('نجح: ' + message);
    }

    // Add new state
    function addNewState() {
        // Remove any existing modal first to prevent duplicates
        $('#add-state-modal').remove();
        
        // Create a modal dialog instead of using prompt
        var modalHtml = `
            <div id="add-state-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 5px; max-width: 400px; width: 90%;">
                    <h3>إضافة ولاية جديدة</h3>
                    <p>
                        <label for="state-code">كود الولاية (مثل: 01):</label><br>
                        <input type="text" id="state-code" style="width: 100%; margin-top: 5px;" placeholder="01">
                    </p>
                    <p>
                        <label for="state-name">اسم الولاية:</label><br>
                        <input type="text" id="state-name" style="width: 100%; margin-top: 5px;" placeholder="اسم الولاية">
                    </p>
                    <div style="text-align: right; margin-top: 20px;">
                        <button type="button" id="cancel-add-state" class="button">إلغاء</button>
                        <button type="button" id="confirm-add-state" class="button button-primary" style="margin-left: 10px;">إضافة</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // Focus on first input
        $('#state-code').focus();

        // Handle cancel
        $('#cancel-add-state').on('click', function() {
            $('#add-state-modal').remove();
        });

        // Handle confirm
        $('#confirm-add-state').on('click', function() {
            var code = $('#state-code').val().trim();
            var name = $('#state-name').val().trim();

            if (!code || !name) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // Disable button during request
            $('#confirm-add-state').prop('disabled', true).text('جاري الإضافة...');

            $.ajax({
                url: rid_cod_states_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'rid_cod_add_state',
                    country_code: currentCountry,
                    state_code: code,
                    state_name: name,
                    nonce: rid_cod_states_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess(response.data.message);
                        $('#add-state-modal').remove();
                        // Add the new state to the table instead of refreshing
                        addStateToTable(code, name);
                    } else {
                        showError(response.data.message || 'خطأ في إضافة الولاية');
                        $('#confirm-add-state').prop('disabled', false).text('إضافة');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                    $('#confirm-add-state').prop('disabled', false).text('إضافة');
                }
            });
        });

        // Handle Enter key
        $('#add-state-modal input').on('keypress', function(e) {
            if (e.which === 13) {
                $('#confirm-add-state').click();
            }
        });

        // Handle Escape key
        $(document).on('keyup.add-state-modal', function(e) {
            if (e.which === 27) {
                $('#add-state-modal').remove();
                $(document).off('keyup.add-state-modal');
            }
        });
    }

    // Edit state
    function editState(button) {
        var oldCode = button.data('state-code');
        var oldName = button.data('state-name');

        // Create a modal dialog for editing
        var modalHtml = `
            <div id="edit-state-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 5px; max-width: 400px; width: 90%;">
                    <h3>تعديل الولاية</h3>
                    <p>
                        <label for="edit-state-code">كود الولاية:</label><br>
                        <input type="text" id="edit-state-code" style="width: 100%; margin-top: 5px;" value="${oldCode}">
                    </p>
                    <p>
                        <label for="edit-state-name">اسم الولاية:</label><br>
                        <input type="text" id="edit-state-name" style="width: 100%; margin-top: 5px;" value="${oldName}">
                    </p>
                    <div style="text-align: right; margin-top: 20px;">
                        <button type="button" id="cancel-edit-state" class="button">إلغاء</button>
                        <button type="button" id="confirm-edit-state" class="button button-primary" style="margin-left: 10px;">حفظ</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // Focus on first input
        $('#edit-state-code').focus().select();

        // Handle cancel
        $('#cancel-edit-state').on('click', function() {
            $('#edit-state-modal').remove();
        });

        // Handle confirm
        $('#confirm-edit-state').on('click', function() {
            var newCode = $('#edit-state-code').val().trim();
            var newName = $('#edit-state-name').val().trim();

            if (!newCode || !newName) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // If nothing changed, just close
            if (newCode === oldCode && newName === oldName) {
                $('#edit-state-modal').remove();
                return;
            }

            // Disable button during request
            $('#confirm-edit-state').prop('disabled', true).text('جاري الحفظ...');

            $.ajax({
                url: rid_cod_states_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'rid_cod_edit_state',
                    country_code: currentCountry,
                    old_code: oldCode,
                    new_code: newCode,
                    new_name: newName,
                    nonce: rid_cod_states_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess(response.data.message);
                        $('#edit-state-modal').remove();
                        // Update the row instead of refreshing
                        updateStateInTable(oldCode, newCode, newName);
                    } else {
                        showError(response.data.message || 'خطأ في تعديل الولاية');
                        $('#confirm-edit-state').prop('disabled', false).text('حفظ');
                    }
                },
                error: function() {
                    showError('خطأ في الاتصال بالخادم');
                    $('#confirm-edit-state').prop('disabled', false).text('حفظ');
                }
            });
        });

        // Handle Enter key
        $('#edit-state-modal input').on('keypress', function(e) {
            if (e.which === 13) {
                $('#confirm-edit-state').click();
            }
        });

        // Handle Escape key
        $(document).on('keyup.edit-state-modal', function(e) {
            if (e.which === 27) {
                $('#edit-state-modal').remove();
                $(document).off('keyup.edit-state-modal');
            }
        });
    }

    // Delete state
    function deleteState(button) {
        var code = button.data('state-code');
        var name = button.data('state-name');
        
        if (!confirm('هل أنت متأكد من حذف الولاية "' + name + '" (' + code + ')؟')) {
            return;
        }

        $.ajax({
            url: rid_cod_states_params.ajax_url,
            type: 'POST',
            data: {
                action: 'rid_cod_delete_state',
                country_code: currentCountry,
                state_code: code,
                nonce: rid_cod_states_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccess(response.data.message);
                    // Remove the row from table instead of refreshing
                    statesTable.find(`tr[data-state-code="${code}"]`).fadeOut(300, function() {
                        $(this).remove();
                        // Show save button if there are still rows
                        if (statesTable.find('.state-row').length > 0) {
                            $('#save-states-order').show();
                        }
                    });
                } else {
                    showError(response.data.message || 'خطأ في حذف الولاية');
                }
            },
            error: function() {
                showError('خطأ في الاتصال بالخادم');
            }
        });
    }

    // Save states order
    function saveStatesOrder() {
        var saveButton = $('#save-states-order');
        saveButton.prop('disabled', true).text('جاري الحفظ...');
        
        var states = [];
        statesTable.find('.state-row').each(function() {
            var row = $(this);
            states.push({
                code: row.data('state-code'),
                name: row.data('state-name')
            });
        });
        
        $.ajax({
            url: rid_cod_states_params.ajax_url,
            type: 'POST',
            data: {
                action: 'rid_cod_save_states',
                country_code: currentCountry,
                states: states,
                nonce: rid_cod_states_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccess(response.data.message || 'تم حفظ الترتيب بنجاح');
                    saveButton.hide();
                    
                    // If this was triggered by form submission, submit the form now
                    if (window.pendingFormSubmission) {
                        window.pendingFormSubmission = false;
                        $('form')[0].submit();
                    }
                } else {
                    showError(response.data.message || 'خطأ في حفظ الترتيب');
                    saveButton.prop('disabled', false).text('حفظ الترتيب');
                }
            },
            error: function() {
                showError('خطأ في الاتصال بالخادم');
                saveButton.prop('disabled', false).text('حفظ الترتيب');
            }
        });
    }

    // Reset to default states
    function resetToDefault() {
        if (!confirm('هل أنت متأكد من استعادة الولايات الافتراضية؟ سيتم حذف جميع التعديلات.')) {
            return;
        }

        $.ajax({
            url: rid_cod_states_params.ajax_url,
            type: 'POST',
            data: {
                action: 'rid_cod_reset_states',
                country_code: currentCountry,
                nonce: rid_cod_states_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccess(response.data.message);
                    refreshStatesTable(); // Refresh table
                } else {
                    showError(response.data.message || 'خطأ في استعادة الولايات الافتراضية');
                }
            },
            error: function() {
                showError('خطأ في الاتصال بالخادم');
            }
        });
    }

    // Monitor country changes from other tabs
    function monitorCountryChanges() {
        // Check if country select exists in country settings tab
        var countrySelect = $('#rid_cod_selected_country');
        if (countrySelect.length > 0) {
            countrySelect.on('change', function() {
                var newCountry = $(this).val();
                if (newCountry !== currentCountry) {
                    currentCountry = newCountry;
                    // Reload page to show new country's states
                    window.location.reload();
                }
            });
        }
    }

    // Update country display in states manager
    function updateCountryDisplay(countryCode) {
        // This would need country data, for now just update with code
        $('.country-name').text(countryCode);
    }

    // Refresh shipping costs table
    function refreshShippingTable() {
        // Simple page reload to refresh the shipping table
        // In a more advanced implementation, this could be done via AJAX
        setTimeout(function() {
            if (confirm('تم تحديث الولايات. هل تريد إعادة تحميل الصفحة لتحديث جدول الأسعار؟')) {
                window.location.reload();
            }
        }, 1000);
    }

    // Add this function to ensure all data is saved
    function ensureDataSaved() {
        // Make sure all state changes are saved
        if ($('#save-states-order').is(':visible')) {
            saveStatesOrder();
            return false; // Prevent form submission until data is saved
        }
        return true; // Allow form submission
    }

    // Handle form submission to ensure all data is saved
    $('form').on('submit', function(e) {
        // Check if there are unsaved state changes
        if ($('#save-states-order').is(':visible')) {
            e.preventDefault();
            window.pendingFormSubmission = true;

            if (confirm('يوجد تغييرات غير محفوظة في ترتيب الولايات. هل تريد حفظها أولاً؟')) {
                // Save states order first, then submit form
                saveStatesOrder();
                // Form will be submitted in the saveStatesOrder success callback
            } else {
                // User chose not to save states, proceed with form submission
                window.pendingFormSubmission = false;
                $('#save-states-order').hide();
                $('form')[0].submit();
            }
        }
        // If no unsaved changes, let form submit normally
    });

    // Event handlers
    $('#add-new-state').on('click', addNewState);
    $('#reset-to-default').on('click', resetToDefault);
    $('#save-states-order').on('click', saveStatesOrder);

    // Delegated event handlers for dynamic content
    $(document).on('click', '.edit-state', function() {
        editState($(this));
    });

    $(document).on('click', '.delete-state', function() {
        deleteState($(this));
    });

    // Initialize the states manager
    initStatesManager();
    monitorCountryChanges();
});
